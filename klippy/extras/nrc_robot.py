
# Klipper extra module for controlling an NRC robot arm.
# This module handles the low-level communication with the robot controller.

import logging
import sys
import os

# --- Path setup to import the nrc_interface library ---
# We need to add the 'lib' directory to Python's search path.
# This assumes the Klipper service runs from the project's root directory.
klipper_base_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
lib_path = os.path.join(klipper_base_dir, 'lib')
if lib_path not in sys.path:
    sys.path.insert(0, lib_path)

try:
    from inexbot import nrc_interface
except ImportError as e:
    raise ImportError(f"Failed to import nrc_interface. Ensure 'lib/inexbot' is accessible. Error: {e}")
# --- End Path setup ---

class NRCRobot:
    def __init__(self, config):
        self.printer = config.get_printer()
        self.name = config.get_name()
        self.logger = logging.getLogger(f"klippy.extras.{self.name}")

        self.ip_address = config.get('ip_address')
        self.port = config.getint('port')
        
        self.socket_fd = -1  # File descriptor for the robot connection

        self.printer.register_event_handler("klippy:connect", self.connect)
        self.printer.register_event_handler("klippy:disconnect", self.disconnect)

        self.logger.info(f"NRC Robot module initialized for {self.ip_address}:{self.port}")

    def connect(self):
        self.logger.info(f"Attempting to connect to NRC robot at {self.ip_address}:{self.port}...")
        try:
            # The connect_robot function returns a socket file descriptor (fd)
            self.socket_fd = nrc_interface.connect_robot(self.ip_address.encode('utf-8'), self.port)
            
            if self.socket_fd < 0:
                self.logger.error(f"Failed to connect to robot. Error code: {self.socket_fd}")
                self.printer.command_error(f"Failed to connect to NRC Robot (code: {self.socket_fd})")
                return

            # Check connection status
            status = nrc_interface.VectorInt(1)
            nrc_interface.get_connection_status(self.socket_fd, status)
            
            if status[0] == 1:
                self.logger.info(f"Successfully connected to NRC robot. Socket FD: {self.socket_fd}")
                # It might be necessary to power on servos after connecting
                self.power_on_servos()
            else:
                self.logger.error("Connection status check failed after connect call.")
                self.printer.command_error("NRC Robot: Connection status check failed.")

        except Exception as e:
            self.logger.error(f"Exception during robot connection: {e}")
            self.printer.command_error(f"Exception connecting to NRC Robot: {e}")

    def disconnect(self):
        if self.socket_fd >= 0:
            self.logger.info(f"Disconnecting from NRC robot (FD: {self.socket_fd})...")
            nrc_interface.disconnect_robot(self.socket_fd)
            self.socket_fd = -1
            self.logger.info("Disconnected.")

    def power_on_servos(self):
        if self.socket_fd < 0:
            self.logger.warning("Cannot power on servos, robot is not connected.")
            return
        
        self.logger.info("Powering on robot servos...")
        # The set_servo_state function likely takes 1 for ON and 0 for OFF.
        ret = nrc_interface.set_servo_state(self.socket_fd, 1)
        if ret != nrc_interface.SUCCESS:
            self.logger.error(f"Failed to power on servos. Error code: {ret}")
            self.printer.command_error(f"Failed to power on NRC servos (code: {ret})")
        else:
            self.logger.info("Servos powered on successfully.")

    def get_status(self):
        # Placeholder for status checking logic
        if self.socket_fd < 0:
            return "Not Connected"
        # In the future, this can return more detailed status
        return f"Connected (FD: {self.socket_fd})"

def load_config(config):
    return NRCRobot(config)

def load_config_prefix(config):
    return NRCRobot(config)

