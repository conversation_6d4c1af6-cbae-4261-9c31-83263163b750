
# Klipper extra module for controlling an NRC robot arm.
# This module handles the low-level communication with the robot controller.

import logging
import sys
import os
import time
import threading

# --- Path setup to import the nrc_interface library ---
# We need to add the 'lib' directory to Python's search path.
# This assumes the Klipper service runs from the project's root directory.
klipper_base_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
lib_path = os.path.join(klipper_base_dir, 'lib')
if lib_path not in sys.path:
    sys.path.insert(0, lib_path)

try:
    from inexbot import nrc_interface as nrc
except ImportError as e:
    raise ImportError(f"Failed to import nrc_interface. Ensure 'lib/inexbot' is accessible. Error: {e}")
# --- End Path setup ---

# Constants for NRC API
SUCCESS = 0
COORD_CARTESIAN = 3  # Cartesian coordinate system
COORD_JOINT = 0      # Joint coordinate system

class NRCRobot:
    def __init__(self, config):
        self.printer = config.get_printer()
        self.name = config.get_name()
        self.logger = logging.getLogger(f"klippy.extras.{self.name}")

        self.ip_address = config.get('ip_address')
        self.port = config.getint('port')

        self.socket_fd = -1  # File descriptor for the robot connection
        self.is_connected = False
        self.servo_powered = False

        # Motion queue management
        self.queue_enabled = False
        self.current_line = 0
        self.queue_lock = threading.Lock()

        self.printer.register_event_handler("klippy:connect", self.connect)
        self.printer.register_event_handler("klippy:disconnect", self.disconnect)

        # Register G-code commands for robot control
        gcode = self.printer.lookup_object('gcode')
        gcode.register_command('NRC_STATUS', self.cmd_nrc_status,
                             desc="Get NRC robot status")
        gcode.register_command('NRC_HOME', self.cmd_nrc_home,
                             desc="Move robot to home position")
        gcode.register_command('NRC_POWER_ON', self.cmd_nrc_power_on,
                             desc="Power on robot servos")
        gcode.register_command('NRC_POWER_OFF', self.cmd_nrc_power_off,
                             desc="Power off robot servos")

        self.logger.info(f"NRC Robot module initialized for {self.ip_address}:{self.port}")

    def connect(self):
        self.logger.info(f"Attempting to connect to NRC robot at {self.ip_address}:{self.port}...")
        try:
            # The connect_robot function returns a socket file descriptor (fd)
            self.socket_fd = nrc.connect_robot(self.ip_address.encode('utf-8'), str(self.port).encode('utf-8'))

            if self.socket_fd < 0:
                self.logger.error(f"Failed to connect to robot. Error code: {self.socket_fd}")
                self.printer.command_error(f"Failed to connect to NRC Robot (code: {self.socket_fd})")
                return

            # Check connection status
            status = nrc.VectorInt()
            status.append(0)
            ret = nrc.get_connection_status(self.socket_fd, status)

            if ret == SUCCESS and status[0] == 1:
                self.is_connected = True
                self.logger.info(f"Successfully connected to NRC robot. Socket FD: {self.socket_fd}")
                # Initialize motion queue
                self._init_motion_queue()
                # Power on servos after connecting
                self.power_on_servos()
            else:
                self.logger.error(f"Connection status check failed. Return code: {ret}, Status: {status[0] if len(status) > 0 else 'N/A'}")
                self.printer.command_error("NRC Robot: Connection status check failed.")

        except Exception as e:
            self.logger.error(f"Exception during robot connection: {e}")
            self.printer.command_error(f"Exception connecting to NRC Robot: {e}")

    def disconnect(self):
        if self.socket_fd >= 0:
            self.logger.info(f"Disconnecting from NRC robot (FD: {self.socket_fd})...")
            # Stop motion queue if running
            if self.queue_enabled:
                self.stop_motion_queue()
            nrc.disconnect_robot(self.socket_fd)
            self.socket_fd = -1
            self.is_connected = False
            self.servo_powered = False
            self.logger.info("Disconnected.")

    def power_on_servos(self):
        if not self.is_connected:
            self.logger.warning("Cannot power on servos, robot is not connected.")
            return False

        self.logger.info("Powering on robot servos...")
        ret = nrc.set_servo_state(self.socket_fd, 1)
        if ret != SUCCESS:
            self.logger.error(f"Failed to power on servos. Error code: {ret}")
            self.printer.command_error(f"Failed to power on NRC servos (code: {ret})")
            return False
        else:
            self.servo_powered = True
            self.logger.info("Servos powered on successfully.")
            return True

    def power_off_servos(self):
        if not self.is_connected:
            self.logger.warning("Cannot power off servos, robot is not connected.")
            return False

        self.logger.info("Powering off robot servos...")
        ret = nrc.set_servo_state(self.socket_fd, 0)
        if ret != SUCCESS:
            self.logger.error(f"Failed to power off servos. Error code: {ret}")
            return False
        else:
            self.servo_powered = False
            self.logger.info("Servos powered off successfully.")
            return True

    def get_status(self):
        """Get comprehensive robot status"""
        if not self.is_connected:
            return {
                'connected': False,
                'servo_powered': False,
                'queue_enabled': False,
                'current_line': 0
            }

        # Get servo status
        servo_status = nrc.VectorInt()
        servo_status.append(0)
        nrc.get_servo_state(self.socket_fd, servo_status)

        # Get robot running state
        running_state = nrc.VectorInt()
        running_state.append(0)
        nrc.get_robot_running_state(self.socket_fd, running_state)

        return {
            'connected': self.is_connected,
            'servo_powered': servo_status[0] == 1 if len(servo_status) > 0 else False,
            'queue_enabled': self.queue_enabled,
            'current_line': self.current_line,
            'running_state': running_state[0] if len(running_state) > 0 else 0
        }

    # === Motion Control Methods ===

    def _init_motion_queue(self):
        """Initialize the motion queue system"""
        try:
            # Enable motion queue
            ret = nrc.queue_motion_set_status(self.socket_fd, 1)
            if ret == SUCCESS:
                self.queue_enabled = True
                self.logger.info("Motion queue enabled successfully")
                # Clear any existing queue data
                nrc.queue_motion_clear_Data(self.socket_fd)
            else:
                self.logger.error(f"Failed to enable motion queue. Error code: {ret}")
        except Exception as e:
            self.logger.error(f"Exception initializing motion queue: {e}")

    def stop_motion_queue(self):
        """Stop and disable the motion queue"""
        if not self.is_connected:
            return False

        try:
            # Stop motion
            nrc.queue_motion_stop(self.socket_fd)
            # Disable queue
            nrc.queue_motion_set_status(self.socket_fd, 0)
            self.queue_enabled = False
            self.logger.info("Motion queue stopped and disabled")
            return True
        except Exception as e:
            self.logger.error(f"Exception stopping motion queue: {e}")
            return False

    def clear_motion_queue(self):
        """Clear all pending motions in the queue"""
        if not self.is_connected or not self.queue_enabled:
            return False

        try:
            with self.queue_lock:
                ret = nrc.queue_motion_clear_Data(self.socket_fd)
                if ret == SUCCESS:
                    self.current_line = 0
                    self.logger.info("Motion queue cleared")
                    return True
                else:
                    self.logger.error(f"Failed to clear motion queue. Error code: {ret}")
                    return False
        except Exception as e:
            self.logger.error(f"Exception clearing motion queue: {e}")
            return False

    def get_current_position(self, coord_type=COORD_CARTESIAN):
        """Get current robot position"""
        if not self.is_connected:
            return None

        try:
            pos = nrc.VectorDouble()
            for i in range(7):  # X, Y, Z, RX, RY, RZ, and extra axis
                pos.append(0.0)

            ret = nrc.get_current_position(self.socket_fd, coord_type, pos)
            if ret == SUCCESS:
                return [pos[i] for i in range(len(pos))]
            else:
                self.logger.error(f"Failed to get current position. Error code: {ret}")
                return None
        except Exception as e:
            self.logger.error(f"Exception getting current position: {e}")
            return None

    def get_joint_positions(self):
        """Get current joint positions"""
        if not self.is_connected:
            return None

        try:
            joint_pos = []
            for axis in range(6):  # 6 joints
                pos = nrc.VectorDouble()
                pos.append(0.0)
                ret = nrc.get_joint_position(self.socket_fd, axis, pos)
                if ret == SUCCESS:
                    joint_pos.append(pos[0])
                else:
                    self.logger.error(f"Failed to get joint {axis} position. Error code: {ret}")
                    return None
            return joint_pos
        except Exception as e:
            self.logger.error(f"Exception getting joint positions: {e}")
            return None

    def move_linear(self, target_pos, velocity=100, acceleration=100, deceleration=100):
        """Add a linear move to the motion queue"""
        if not self.is_connected or not self.queue_enabled:
            self.logger.error("Cannot move: robot not connected or queue not enabled")
            return False

        try:
            with self.queue_lock:
                # Create move command structure
                move_cmd = nrc.MoveCmd()
                move_cmd.targetPosType = COORD_CARTESIAN
                move_cmd.vel = velocity
                move_cmd.acc = acceleration
                move_cmd.dec = deceleration

                # Set target position
                pos_data = nrc.VectorDouble()
                for coord in target_pos:
                    pos_data.append(float(coord))

                # Ensure we have at least 6 coordinates (X,Y,Z,RX,RY,RZ)
                while len(pos_data) < 6:
                    pos_data.append(0.0)

                move_cmd.targetPosValue = pos_data

                # Add to queue
                ret = nrc.queue_motion_push_back_moveL(self.socket_fd, move_cmd)
                if ret == SUCCESS:
                    self.logger.debug(f"Added linear move to queue: {target_pos}")
                    return True
                else:
                    self.logger.error(f"Failed to add linear move to queue. Error code: {ret}")
                    return False
        except Exception as e:
            self.logger.error(f"Exception adding linear move: {e}")
            return False

    def move_joint(self, joint_angles, velocity=100, acceleration=100, deceleration=100):
        """Add a joint move to the motion queue"""
        if not self.is_connected or not self.queue_enabled:
            self.logger.error("Cannot move: robot not connected or queue not enabled")
            return False

        try:
            with self.queue_lock:
                # Create move command structure
                move_cmd = nrc.MoveCmd()
                move_cmd.targetPosType = COORD_JOINT
                move_cmd.vel = velocity
                move_cmd.acc = acceleration
                move_cmd.dec = deceleration

                # Set joint angles
                pos_data = nrc.VectorDouble()
                for angle in joint_angles:
                    pos_data.append(float(angle))

                # Ensure we have exactly 6 joint angles
                while len(pos_data) < 6:
                    pos_data.append(0.0)

                move_cmd.targetPosValue = pos_data

                # Add to queue
                ret = nrc.queue_motion_push_back_moveJ(self.socket_fd, move_cmd)
                if ret == SUCCESS:
                    self.logger.debug(f"Added joint move to queue: {joint_angles}")
                    return True
                else:
                    self.logger.error(f"Failed to add joint move to queue. Error code: {ret}")
                    return False
        except Exception as e:
            self.logger.error(f"Exception adding joint move: {e}")
            return False

    def send_motion_batch(self, batch_size=None):
        """Send queued motions to the robot controller"""
        if not self.is_connected or not self.queue_enabled:
            return False

        try:
            with self.queue_lock:
                # Get current queue length
                queue_len = nrc.VectorInt()
                queue_len.append(0)
                ret = nrc.queue_motion_get_queuelen(self.socket_fd, queue_len)

                if ret == SUCCESS and queue_len[0] > 0:
                    send_size = batch_size if batch_size else queue_len[0]
                    ret = nrc.queue_motion_send_to_controller(self.socket_fd, send_size)
                    if ret == SUCCESS:
                        self.logger.debug(f"Sent {send_size} motions to controller")
                        return True
                    else:
                        self.logger.error(f"Failed to send motions to controller. Error code: {ret}")
                        return False
                else:
                    self.logger.debug("No motions in queue to send")
                    return True
        except Exception as e:
            self.logger.error(f"Exception sending motion batch: {e}")
            return False

    def get_current_line_number(self):
        """Get the current executing line number"""
        if not self.is_connected:
            return -1

        try:
            line = nrc.VectorInt()
            line.append(0)
            ret = nrc.job_get_current_line(self.socket_fd, line)
            if ret == SUCCESS:
                self.current_line = line[0]
                return line[0]
            else:
                return -1
        except Exception as e:
            self.logger.error(f"Exception getting current line: {e}")
            return -1

    def home_robot(self):
        """Move robot to home position"""
        if not self.is_connected or not self.servo_powered:
            self.logger.error("Cannot home: robot not connected or servos not powered")
            return False

        try:
            ret = nrc.robot_go_home(self.socket_fd)
            if ret == SUCCESS:
                self.logger.info("Robot homing initiated")
                return True
            else:
                self.logger.error(f"Failed to home robot. Error code: {ret}")
                return False
        except Exception as e:
            self.logger.error(f"Exception homing robot: {e}")
            return False

    # === G-code Command Handlers ===

    def cmd_nrc_status(self, gcmd):
        """Handle NRC_STATUS G-code command"""
        status = self.get_status()
        gcmd.respond_info(f"NRC Robot Status:\n"
                         f"  Connected: {status['connected']}\n"
                         f"  Servo Powered: {status['servo_powered']}\n"
                         f"  Queue Enabled: {status['queue_enabled']}\n"
                         f"  Current Line: {status['current_line']}\n"
                         f"  Running State: {status['running_state']}")

        if status['connected']:
            pos = self.get_current_position()
            if pos:
                gcmd.respond_info(f"  Current Position: X={pos[0]:.3f} Y={pos[1]:.3f} Z={pos[2]:.3f}\n"
                                f"                   RX={pos[3]:.3f} RY={pos[4]:.3f} RZ={pos[5]:.3f}")

    def cmd_nrc_home(self, gcmd):
        """Handle NRC_HOME G-code command"""
        if self.home_robot():
            gcmd.respond_info("Robot homing initiated")
        else:
            gcmd.respond_error("Failed to initiate robot homing")

    def cmd_nrc_power_on(self, gcmd):
        """Handle NRC_POWER_ON G-code command"""
        if self.power_on_servos():
            gcmd.respond_info("Robot servos powered on")
        else:
            gcmd.respond_error("Failed to power on robot servos")

    def cmd_nrc_power_off(self, gcmd):
        """Handle NRC_POWER_OFF G-code command"""
        if self.power_off_servos():
            gcmd.respond_info("Robot servos powered off")
        else:
            gcmd.respond_error("Failed to power off robot servos")

def load_config(config):
    return NRCRobot(config)

def load_config_prefix(config):
    return NRCRobot(config)

