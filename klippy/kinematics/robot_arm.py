
# Klipper kinematics for a 6-axis robot arm.

import logging
import math
import time
import threading
try:
    import numpy as np
except ImportError:
    # Fallback if numpy is not available
    np = None

class RobotArmKinematics:
    def __init__(self, toolhead, config):
        self.printer = config.get_printer()
        self.logger = logging.getLogger(__name__)
        self.toolhead = toolhead

        # Lookup the nrc_robot communication object
        self.nrc_robot = self.printer.lookup_object('nrc_robot', default=None)
        if self.nrc_robot is None:
            raise config.error("The [nrc_robot] config section must be present.")

        # Configure robot joints (steppers)
        # Klipper requires a stepper for each axis of the kinematics.
        self.steppers = []
        for i in range(6):
            stepper = config.get_printer().lookup_object(f'stepper_j{i}')
            self.steppers.append(stepper)

        # Set stepper callbacks
        self.toolhead.set_kinematics(self, self.steppers)

        # Load parameters from [robot_arm] section
        self.max_velocity, self.max_accel = self.toolhead.get_max_velocity()
        self.max_z_velocity = config.getfloat('max_z_velocity', self.max_velocity, above=0.)
        self.max_z_accel = config.getfloat('max_z_accel', self.max_accel, above=0.)

        # Load robot physical parameters
        self._load_robot_params(config)

        # Motion batch management for PBPS model
        self.move_batch = []
        self.batch_size = config.getint('batch_size', 50, minval=1, maxval=200)
        self.batch_lock = threading.Lock()

        # Current position tracking
        self.last_position = [0., 0., 0., 0., 0., 0.]  # X, Y, Z, RX, RY, RZ

        # Register G-code commands
        gcode = self.printer.lookup_object('gcode')
        gcode.register_command('ARM_GET_POSITION', self.cmd_get_position,
                             desc="Get current robot arm position")
        gcode.register_command('ARM_MOVE_TO', self.cmd_move_to,
                             desc="Move robot arm to specified position")

        self.logger.info("Robot Arm Kinematics initialized")

    def _load_robot_params(self, config):
        """Load robot physical parameters from config"""
        # Work area limits
        self.min_x = config.getfloat('min_x', -500., below=0.)
        self.max_x = config.getfloat('max_x', 500., above=0.)
        self.min_y = config.getfloat('min_y', -500., below=0.)
        self.max_y = config.getfloat('max_y', 500., above=0.)
        self.min_z = config.getfloat('min_z', 0., above=-100.)
        self.max_z = config.getfloat('max_z', 800., above=0.)

        # Rotation limits (in radians)
        self.min_rx = config.getfloat('min_rx', -math.pi)
        self.max_rx = config.getfloat('max_rx', math.pi)
        self.min_ry = config.getfloat('min_ry', -math.pi)
        self.max_ry = config.getfloat('max_ry', math.pi)
        self.min_rz = config.getfloat('min_rz', -math.pi)
        self.max_rz = config.getfloat('max_rz', math.pi)

        # Joint limits (in radians)
        self.joint_limits = []
        for i in range(6):
            min_joint = config.getfloat(f'min_joint_{i}', -math.pi)
            max_joint = config.getfloat(f'max_joint_{i}', math.pi)
            self.joint_limits.append((min_joint, max_joint))

        # DH parameters (placeholder - should be loaded from robot specs)
        # For now, we'll use dummy values
        self.dh_params = [
            # [a, alpha, d, theta_offset]
            [0, math.pi/2, 0.1, 0],      # Joint 1
            [0.3, 0, 0, 0],              # Joint 2
            [0.25, 0, 0, 0],             # Joint 3
            [0, math.pi/2, 0.15, 0],     # Joint 4
            [0, -math.pi/2, 0, 0],       # Joint 5
            [0, 0, 0.1, 0]               # Joint 6
        ]

        self.logger.info(f"Robot work area: X[{self.min_x}, {self.max_x}] "
                        f"Y[{self.min_y}, {self.max_y}] Z[{self.min_z}, {self.max_z}]")

    def get_steppers(self):
        return self.steppers

    def calc_position(self, stepper_positions):
        """Calculate Cartesian coordinates from joint angles (Forward Kinematics)."""
        # Since we don't have real stepper feedback from the robot arm,
        # we get the actual position from the robot controller
        if self.nrc_robot and self.nrc_robot.is_connected:
            pos = self.nrc_robot.get_current_position()
            if pos and len(pos) >= 6:
                self.last_position = pos[:6]
                return pos[:3]  # Return only X, Y, Z for Klipper

        # Fallback to last known position
        return self.last_position[:3]

    def check_move(self, move):
        """Check if the requested move is within the robot's configured limits."""
        end_pos = move.end_pos

        # Check Cartesian position limits
        if (end_pos[0] < self.min_x or end_pos[0] > self.max_x or
            end_pos[1] < self.min_y or end_pos[1] > self.max_y or
            end_pos[2] < self.min_z or end_pos[2] > self.max_z):
            raise move.move_error(f"Move to ({end_pos[0]:.3f}, {end_pos[1]:.3f}, {end_pos[2]:.3f}) "
                                f"exceeds work area limits")

        # Check orientation limits if provided (for 6D moves)
        if len(end_pos) >= 6:
            if (end_pos[3] < self.min_rx or end_pos[3] > self.max_rx or
                end_pos[4] < self.min_ry or end_pos[4] > self.max_ry or
                end_pos[5] < self.min_rz or end_pos[5] > self.max_rz):
                raise move.move_error(f"Move orientation ({end_pos[3]:.3f}, {end_pos[4]:.3f}, {end_pos[5]:.3f}) "
                                    f"exceeds rotation limits")

        # Check if position is reachable (basic check)
        if not self._is_position_reachable(end_pos):
            raise move.move_error(f"Position ({end_pos[0]:.3f}, {end_pos[1]:.3f}, {end_pos[2]:.3f}) "
                                f"is not reachable by the robot")

    def _is_position_reachable(self, pos):
        """Basic reachability check"""
        # Simple distance check from origin
        distance = math.sqrt(pos[0]**2 + pos[1]**2 + pos[2]**2)
        max_reach = 1.0  # Approximate maximum reach, should be calculated from DH parameters
        return distance <= max_reach

    def move(self, newpos, speed):
        """This is the core function that will be called by Klipper to execute a move."""
        # This is the main entry point for a move command from the toolhead.
        # In this initial phase, we will just log the requested move.
        # In the next step, we will call the nrc_robot module to send the command.
        self.logger.info(f"Move requested to {newpos} at speed {speed}")

        # The actual movement command will be sent from here later.
        # For example:
        # self.nrc_robot.move_linear(newpos, speed)

        # We must immediately update the toolhead position, as Klipper expects this.
        self.toolhead.set_position(newpos, homing_axes=())

# Klipper's entry point for this kinematics module
def load_config(config):
    return RobotArmKinematics(config.get_printer().lookup_object('toolhead'), config)

def load_config_prefix(config):
    return RobotArmKinematics(config.get_printer().lookup_object('toolhead'), config)
